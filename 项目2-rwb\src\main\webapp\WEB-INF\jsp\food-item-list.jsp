<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高校食堂食材管理系统</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/assets/css/main.css">
    <script src="${pageContext.request.contextPath}/assets/js/jquery.min.js"></script>
</head>
<body>
    <div class="main-container">
        <header class="page-header">
            <h1>高校食堂食材管理系统</h1>
        </header>
        
        <!-- 查询条件区域 -->
        <section class="query-section">
            <div class="query-form-wrapper">
                <form id="queryForm" action="${pageContext.request.contextPath}/foodItem/query" method="post">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="foodType">所属类别：</label>
                            <input type="text" id="foodType" name="foodType" value="${queryType}" 
                                   placeholder="输入食材类别" class="form-input">
                        </div>
                        <div class="form-group">
                            <label for="foodName">食材名称：</label>
                            <input type="text" id="foodName" name="foodName" value="${queryName}" 
                                   placeholder="输入食材名称" class="form-input">
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">搜索</button>
                            <button type="button" class="btn btn-secondary" onclick="clearQuery()">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </section>
        
        <!-- 数据展示区域 -->
        <section class="data-section">
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th width="8%">编号</th>
                            <th width="20%">食材名称</th>
                            <th width="15%">所属类别</th>
                            <th width="12%">采购价格(元)</th>
                            <th width="15%">采购人员</th>
                            <th width="20%">采购时间</th>
                            <th width="10%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:if test="${not empty foodItemList}">
                            <c:forEach var="item" items="${foodItemList}" varStatus="loop">
                                <tr class="data-row">
                                    <td class="text-center">${item.id}</td>
                                    <td>${item.name}</td>
                                    <td>${item.type}</td>
                                    <td class="text-right">
                                        <c:if test="${item.price != null}">
                                            <fmt:formatNumber value="${item.price}" pattern="0.0"/>
                                        </c:if>
                                        <c:if test="${item.price == null}">-</c:if>
                                    </td>
                                    <td>${item.buyer}</td>
                                    <td class="text-center">
                                        <fmt:formatDate value="${item.buyTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </td>
                                    <td class="text-center">
                                        <a href="javascript:void(0)" class="delete-link" 
                                           onclick="confirmDelete(${item.id}, '${item.name}')">删除</a>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        <c:if test="${empty foodItemList}">
                            <tr>
                                <td colspan="7" class="no-data-message">没有找到符合条件的数据</td>
                            </tr>
                        </c:if>
                    </tbody>
                </table>
            </div>
        </section>
        
        <!-- 统计信息区域 -->
        <footer class="statistics-section">
            <div class="statistics-info">
                共为您查询到 <strong class="highlight-number">${totalCount}</strong> 条符合条件的信息
            </div>
        </footer>
    </div>
    
    <script>
        // 清空查询条件
        function clearQuery() {
            window.location.href = '${pageContext.request.contextPath}/foodItem/index';
        }
        
        // 确认删除操作
        function confirmDelete(id, name) {
            if (confirm('确定要删除食材"' + name + '"吗？')) {
                performDelete(id);
            }
        }
        
        // 执行删除操作
        function performDelete(id) {
            $.ajax({
                url: '${pageContext.request.contextPath}/foodItem/delete',
                type: 'POST',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败：' + response.message);
                    }
                },
                error: function() {
                    alert('删除操作失败，请稍后重试！');
                }
            });
        }
    </script>
</body>
</html>

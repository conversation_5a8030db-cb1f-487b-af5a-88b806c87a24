package cn.rwb.controller;

import cn.rwb.model.FoodItem;
import cn.rwb.service.FoodItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 食材管理控制器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-16
 */
@Controller
@RequestMapping("/foodItem")
public class FoodItemController {
    
    @Autowired
    private FoodItemService foodItemService;
    
    /**
     * 显示食材管理主页面
     * 
     * @param model 视图模型
     * @return 页面路径
     */
    @RequestMapping(value = "/index", method = RequestMethod.GET)
    public String showIndexPage(Model model) {
        // 获取所有食材数据
        List<FoodItem> foodItemList = foodItemService.queryAllFoodItems();
        int totalCount = foodItemService.getTotalFoodItemCount();
        
        // 设置模型属性
        model.addAttribute("foodItemList", foodItemList);
        model.addAttribute("totalCount", totalCount);
        model.addAttribute("queryType", "");
        model.addAttribute("queryName", "");
        
        return "food-item-list";
    }
    
    /**
     * 执行食材查询操作
     * 
     * @param foodType 食材类型
     * @param foodName 食材名称
     * @param model 视图模型
     * @return 页面路径
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public String queryFoodItems(@RequestParam(value = "foodType", required = false) String foodType,
                                @RequestParam(value = "foodName", required = false) String foodName,
                                Model model) {
        
        // 执行查询操作
        List<FoodItem> foodItemList = foodItemService.queryFoodItemsByCondition(foodType, foodName);
        int totalCount = foodItemService.getFoodItemCountByCondition(foodType, foodName);
        
        // 设置模型属性
        model.addAttribute("foodItemList", foodItemList);
        model.addAttribute("totalCount", totalCount);
        model.addAttribute("queryType", foodType != null ? foodType : "");
        model.addAttribute("queryName", foodName != null ? foodName : "");
        
        return "food-item-list";
    }
    
    /**
     * 删除食材记录
     * 
     * @param id 食材ID
     * @return JSON响应
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> deleteFoodItem(@RequestParam("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = foodItemService.removeFoodItemById(id);
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除操作异常：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 首页重定向
     * 
     * @return 重定向路径
     */
    @RequestMapping("/")
    public String redirectToIndex() {
        return "redirect:/foodItem/index";
    }
}

package cn.rwb.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 食材信息实体
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-16
 */
public class FoodItem {
    
    // 主键ID
    private Integer id;
    
    // 食材名称
    private String name;
    
    // 食材分类
    private String type;
    
    // 单价
    private BigDecimal price;
    
    // 采购员
    private String buyer;
    
    // 采购日期
    private Date buyTime;
    
    /**
     * 默认构造函数
     */
    public FoodItem() {
        super();
    }
    
    /**
     * 带参数构造函数
     */
    public FoodItem(String name, String type, BigDecimal price, String buyer) {
        this.name = name;
        this.type = type;
        this.price = price;
        this.buyer = buyer;
    }
    
    /**
     * 获取ID
     */
    public Integer getId() {
        return this.id;
    }
    
    /**
     * 设置ID
     */
    public void setId(Integer id) {
        this.id = id;
    }
    
    /**
     * 获取食材名称
     */
    public String getName() {
        return this.name;
    }
    
    /**
     * 设置食材名称
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * 获取食材分类
     */
    public String getType() {
        return this.type;
    }
    
    /**
     * 设置食材分类
     */
    public void setType(String type) {
        this.type = type;
    }
    
    /**
     * 获取单价
     */
    public BigDecimal getPrice() {
        return this.price;
    }
    
    /**
     * 设置单价
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    /**
     * 获取采购员
     */
    public String getBuyer() {
        return this.buyer;
    }
    
    /**
     * 设置采购员
     */
    public void setBuyer(String buyer) {
        this.buyer = buyer;
    }
    
    /**
     * 获取采购日期
     */
    public Date getBuyTime() {
        return this.buyTime;
    }
    
    /**
     * 设置采购日期
     */
    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }
    
    /**
     * 重写toString方法
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("FoodItem [");
        sb.append("id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", type=").append(type);
        sb.append(", price=").append(price);
        sb.append(", buyer=").append(buyer);
        sb.append(", buyTime=").append(buyTime);
        sb.append("]");
        return sb.toString();
    }
}

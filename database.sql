-- 食堂食材采购系统数据库脚本
-- 创建时间: 2024-06-16
-- 数据库名: buyer_db

-- 创建数据库
DROP DATABASE IF EXISTS buyer_db;
CREATE DATABASE buyer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE buyer_db;

-- 创建食材信息表
CREATE TABLE tb_foods (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '编号',
    name VARCHAR(50) NOT NULL COMMENT '名称',
    type VARCHAR(50) NOT NULL COMMENT '类别',
    price DECIMAL(9,1) COMMENT '价格',
    buyer VARCHAR(20) NOT NULL COMMENT '采购人',
    buy_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '采购时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食材信息表';

-- 插入测试数据
INSERT INTO tb_foods (name, type, price, buyer, buy_time) VALUES
('土豆', '蔬菜类', 3.5, '张三', '2024-06-15 09:30:00'),
('猪肉', '肉类', 28.0, '李四', '2024-06-14 14:20:00'),
('大米', '主食类', 4.2, '王五', '2024-06-13 11:15:00'),
('白菜', '蔬菜类', 2.8, '赵六', '2024-06-12 16:45:00'),
('西红柿', '蔬菜类', 4.5, '钱七', '2024-06-16 10:30:00'),
('牛肉', '肉类', 45.0, '孙八', '2024-06-15 15:20:00'),
('面粉', '主食类', 3.8, '周九', '2024-06-14 09:15:00'),
('青椒', '蔬菜类', 6.2, '吴十', '2024-06-13 14:40:00');

-- 查询验证数据
SELECT * FROM tb_foods ORDER BY buy_time DESC;

-- 显示表结构
DESC tb_foods;

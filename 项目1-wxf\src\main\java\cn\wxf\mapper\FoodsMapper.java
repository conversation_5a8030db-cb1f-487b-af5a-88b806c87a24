package cn.wxf.mapper;

import cn.wxf.entity.FoodsInfo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 食材信息数据访问接口
 * <AUTHOR>
 * @date 2024-06-16
 */
public interface FoodsMapper {
    
    /**
     * 查询所有食材信息，按采购时间降序排列
     * @return 食材信息列表
     */
    List<FoodsInfo> findAllFoods();
    
    /**
     * 根据条件查询食材信息
     * @param type 食材类别
     * @param name 食材名称
     * @return 符合条件的食材信息列表
     */
    List<FoodsInfo> findFoodsByCondition(@Param("type") String type, @Param("name") String name);
    
    /**
     * 统计食材总数量
     * @return 总数量
     */
    int countAllFoods();
    
    /**
     * 根据条件统计食材数量
     * @param type 食材类别
     * @param name 食材名称
     * @return 符合条件的数量
     */
    int countFoodsByCondition(@Param("type") String type, @Param("name") String name);
}

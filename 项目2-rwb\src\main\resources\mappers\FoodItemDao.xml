<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.rwb.dao.FoodItemDao">
    
    <!-- 食材信息结果映射 -->
    <resultMap id="FoodItemResultMap" type="cn.rwb.model.FoodItem">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="buyer" property="buyer" jdbcType="VARCHAR"/>
        <result column="buy_time" property="buyTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 查询所有食材信息 -->
    <select id="selectAllFoodItems" resultMap="FoodItemResultMap">
        SELECT 
            id,
            name,
            type,
            price,
            buyer,
            buy_time
        FROM tb_foods
        ORDER BY buy_time DESC
    </select>
    
    <!-- 条件查询食材信息 -->
    <select id="selectFoodItemsByCondition" resultMap="FoodItemResultMap">
        SELECT 
            id,
            name,
            type,
            price,
            buyer,
            buy_time
        FROM tb_foods
        <where>
            <if test="foodType != null and foodType != ''">
                type LIKE CONCAT('%', #{foodType}, '%')
            </if>
            <if test="foodName != null and foodName != ''">
                AND name LIKE CONCAT('%', #{foodName}, '%')
            </if>
        </where>
        ORDER BY buy_time DESC
    </select>
    
    <!-- 根据ID删除食材 -->
    <delete id="deleteFoodItemById">
        DELETE FROM tb_foods WHERE id = #{id}
    </delete>
    
    <!-- 统计食材总数 -->
    <select id="countTotalFoodItems" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM tb_foods
    </select>
    
    <!-- 条件统计食材数量 -->
    <select id="countFoodItemsByCondition" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tb_foods
        <where>
            <if test="foodType != null and foodType != ''">
                type LIKE CONCAT('%', #{foodType}, '%')
            </if>
            <if test="foodName != null and foodName != ''">
                AND name LIKE CONCAT('%', #{foodName}, '%')
            </if>
        </where>
    </select>
    
</mapper>

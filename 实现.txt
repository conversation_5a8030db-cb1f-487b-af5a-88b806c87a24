帮我实现两个一模一样的小实验（项目1包名：cn.wxf 项目2包名：cn.rwb） 需要上交给老师 虽然要求一样但是要做的有差别些包括命名 内容书写格式这些 不能看起来是同一个人做的 作者就对应包名后面的wxf和rwb
写下实现文档 然后分别实现好项目1和项目2 sql语句统一写到mapper.xml文件中 不要放到java文件中 
分数达到85分即可 不要满分（业务功能65分 不需要全部实现 自己看着分配给两个项目 严格对应这里的要求 最简单化实现好给我）
一、语言和环境 
1.实现语言：JAVA语言(JDK8)。 
2.环境要求： Idea/Tomcat/MySQL。
3.使用技术：Spring MVC + Spring + MyBatis。
4.功能要求：不得使用第三方工具生成实体类、持久层代码，否则不得分。 
二、实现功能 
某高校食堂为方便管理采购的食材信息，需要随时统计筛查食材，以明确现有食材和需要采购食材信息，要求开发一套食材采购系统，实现对食材信息统一化管理，具体实现要求如下： 
1.首页上方显示为查询表单，包括：所属类别、食材名称两个查询关键字条件，下方显示所有已经录入的食材信息列表，默认要求按照采购时间降序排列，如图1所示。 
（1）按“采购时间”降序排列。 
（2）底部显示共为您查询到XX条符合条件的信息。 
 图1 所有已采购食材列表信息 
2.输入类别、名称关键字，点击“搜索”按钮可以根据这二个字段条件进行模糊搜索（多个条件按并且关系处理），将搜索的信息展示在列表中，如图2（单条件查询）、图3（多条件同时查询）所示。 
  图2 单条件查询食材信息 
 图3 多条件查询食材信息 
3.点击列表每行末尾的“删除”链接，将当前食材信息进行删除操作。 
a)删除前需要给予提示，如图4所示 
b)删除后需要刷新列表信息，此时列表中应不包含已经删除的信息，如图5所示 
 
 图4 删除采购信息提示 
 图5 删除成功列表刷新
三、 数据库设计 
1.创建数据库（buyer_db）。 
2.创建数据表（tb_foods），结构如下。 
字段名 	说明 	字段类型 	长度 	备注 
id 	编号 	int 	 	主键，自增，增量为1 
name 	名称 	varchar 	50 	不能为空 
type 	类别 	varchar 	50 	不能为空 
price 	价格 	decimal 	9,1 	可以为空 
buyer 	采购人 	varchar 	20 	不能为空 
buy_time 	采购时间 	datetime 	20 	添加时间默认当前时间 

四、具体要求及推荐实现步骤 
SSM版本推荐实现步骤如下： 
（1）创建数据库和数据表，添加测试数据（至少添加4条测试数据）。 
（2）创建Web工程并创建各个包，导入工程所需的jar文件。 
（3）添加相关SSM框架支持。 
（4）配置项目所需要的各种配置文件（mybatis配置文件、spring配置文件）。 
（5）创建实体类FoodsInfo。 
（6）创建MyBatis操作数据库所需的Mapper接口及其Xml映射数据库操作语句文件。 
（7）创建业务逻辑相应的接口及其实现类，实现相应的业务，并在类中加入对DAO/Mapper的引用和注入。 
（8）创建Controller控制器类，在Controller中添加对业务逻辑类的引用和注入，并配置springMVC 配置文件。 
（9）创建相关的操作页面，并使用CSS对页面进行美化。 
（10）实现页面的各项操作功能，并在相关地方进行验证，操作要人性化。 
（11）调试运行成功后导出相关的数据库文件并提交。 

得分要求如下
五、评分标准 
题目：食堂食材采购系统 
使用SSM框架实现该项目评分标准（满分100）： 分数达到85分即可 不要满分（业务功能65分 不需要全部实现 自己看着分配给两个项目 严格对应这里的要求 最简单化实现好给我）
5 	数据库（以数据库脚本方式提交）（5分）：创建并测试数据库脚本（3分）；添加测试数据（2分） 
5 	正确创建和编写实体类，包含所有属性（3分），及getter/setter方法（2分） 
20 	SSM整合 
	Spring配置文件(5分)：正确完成数据源DataSource相关配置（3分）；正确完成Spring中所有Bean的扫描及开启依赖注入注解（2分） 
	Mybatis相关配置(5分)：正确完成spring配置文件中mybatis-config.xml、各个Mapper以及实体类的相关配置
（3分）；正确完成SqlSessionFactory相关配置（2分） 
	SpringMvc配置（5分）：正确完成视图解析器的配置（3分）；正确打开依赖注入相关配置（2分） 
	Web.xml相关配置（5分）：正确完成SpringMVC核心控制器及监听器配置（3分）；正确完成Spring配置文件及
SpringMVC配置文件路径位置的配置（2分） 
65 	业务功能实现 
 	查询功能持久层设计(12分)：正确完成创建并编写Mapper.xml以及完成编写Select节点完成查询（3分）；多条件模糊查询（3分）；正确编写Mapper接口并定义查询方法（3分）；数据排序（3分） 
	查询业务功能层设计(8分)：正确创建业务逻辑层类（2分）；正确实现Mapper持久层的对象注入（3分）；正确编写业务层类完成Mapper接口查询方法调用并返回查询的结果集（3分） 
	查询功能控制器设计(13 分)：正确定义控制器类，并定义属性完成业务层接口实现类的注入（2 分）；在控制器类中定义查询方法用于处理查询请求，并完成正确注解（3分）；正确定义接收查询表单传递的参数（3分）；正确完成针对业务层查询方法的调用并获取到查询结果（3分）；正确完成将查询结果存入到作用域并完成转发（2分） 
	查询功能列表展示页面设计(12分)：正确定义查询条件表单，包括表单action地址（2分）；正确设计数据展示表格（2分）；正确使用JSTL+EL表达式完成查询结果展示（2分）；正确实现每个“删除”超链接，包括href地址正确（2分）；正确实现点击“删除”链接进行确认提示（2分）；底部展示结果数量（2分） 
	删除功能持久层设计(6分)：正确完成持久层Mapper接口根据ID删除数据方法定义（3分）；正确完成Mapper.xml中删除数据Sql语句编写（3分） 
	删除功能业务层设计(5分)：正确定义业务逻辑层中删除方法（2分）；正确调用Mapper实现删除方法（3分） 
	删除功能控制器设计(9分)：正确完成控制器中删除方法定义及注解（1分）；正确获取请求的编号（2分）；正确完成业务层方法调用（3分）；正确实现删除成功后页面自动刷新最新数据（3分） 
5 	总体编程技术（5分）：编码命名规范（2分）；程序正常运行，无异常（3分） 
总分 	100分 

六、核心步骤截图。
将核心步骤(对应第四点 具体要求及推荐实现步骤 截图部分预留给我)按顺序且附带说明截图如下：


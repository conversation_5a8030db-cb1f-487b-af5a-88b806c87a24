package cn.rwb.service.impl;

import cn.rwb.dao.FoodItemDao;
import cn.rwb.model.FoodItem;
import cn.rwb.service.FoodItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 食材业务服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-16
 */
@Service("foodItemService")
@Transactional
public class FoodItemServiceImpl implements FoodItemService {
    
    @Autowired
    private FoodItemDao foodItemDao;
    
    /**
     * 查询所有食材数据
     */
    @Override
    @Transactional(readOnly = true)
    public List<FoodItem> queryAllFoodItems() {
        return foodItemDao.selectAllFoodItems();
    }
    
    /**
     * 根据条件查询食材
     */
    @Override
    @Transactional(readOnly = true)
    public List<FoodItem> queryFoodItemsByCondition(String foodType, String foodName) {
        // 参数预处理
        String processedType = processSearchParam(foodType);
        String processedName = processSearchParam(foodName);
        
        // 如果参数都为空，返回所有数据
        if (processedType == null && processedName == null) {
            return this.queryAllFoodItems();
        }
        
        return foodItemDao.selectFoodItemsByCondition(processedType, processedName);
    }
    
    /**
     * 删除指定食材
     */
    @Override
    public boolean removeFoodItemById(Integer id) {
        if (id == null || id <= 0) {
            return false;
        }
        
        int result = foodItemDao.deleteFoodItemById(id);
        return result > 0;
    }
    
    /**
     * 获取食材总数量
     */
    @Override
    @Transactional(readOnly = true)
    public int getTotalFoodItemCount() {
        return foodItemDao.countTotalFoodItems();
    }
    
    /**
     * 根据条件获取食材数量
     */
    @Override
    @Transactional(readOnly = true)
    public int getFoodItemCountByCondition(String foodType, String foodName) {
        // 参数预处理
        String processedType = processSearchParam(foodType);
        String processedName = processSearchParam(foodName);
        
        // 如果参数都为空，返回总数
        if (processedType == null && processedName == null) {
            return this.getTotalFoodItemCount();
        }
        
        return foodItemDao.countFoodItemsByCondition(processedType, processedName);
    }
    
    /**
     * 处理搜索参数
     * 
     * @param param 原始参数
     * @return 处理后的参数
     */
    private String processSearchParam(String param) {
        if (param == null) {
            return null;
        }
        
        String trimmed = param.trim();
        return trimmed.isEmpty() ? null : trimmed;
    }
}

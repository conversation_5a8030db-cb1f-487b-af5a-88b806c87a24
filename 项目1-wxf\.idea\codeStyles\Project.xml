<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <HTMLCodeStyleSettings>
      <option name="HTML_ATTRIBUTE_WRAP" value="0" />
      <option name="HTML_TEXT_WRAP" value="0" />
      <option name="HTML_DO_NOT_INDENT_CHILDREN_OF" value="thead,tbody,tfoot" />
      <option name="HTML_ENFORCE_QUOTES" value="true" />
    </HTMLCodeStyleSettings>
    <ScalaCodeStyleSettings>
      <option name="MULTILINE_STRING_CLOSING_QUOTES_ON_NEW_LINE" value="true" />
    </ScalaCodeStyleSettings>
    <XML>
      <option name="XML_KEEP_WHITESPACES" value="true" />
    </XML>
    <codeStyleSettings language="HTML">
      <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
      <indentOptions>
        <option name="KEEP_INDENTS_ON_EMPTY_LINES" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JAVA">
      <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
      <option name="BLOCK_COMMENT_AT_FIRST_COLUMN" value="false" />
      <option name="LINE_COMMENT_ADD_SPACE" value="true" />
    </codeStyleSettings>
    <codeStyleSettings language="JSP">
      <option name="WRAP_ON_TYPING" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="XML">
      <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
      <option name="BLOCK_COMMENT_AT_FIRST_COLUMN" value="false" />
      <option name="BLOCK_COMMENT_ADD_SPACE" value="true" />
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>
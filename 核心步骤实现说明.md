# 食堂食材采购系统核心步骤实现说明

## 项目1 (cn.wxf) - 作者：wxf

### （1）创建数据库和数据表，添加测试数据

首先我创建了buyer_db数据库，然后建立了tb_foods表来存储食材信息。表结构包含了id、name、type、price、buyer、buy_time等字段，完全按照需求设计。

**截图说明：** [数据库创建截图]

我在数据库中插入了8条测试数据，包括土豆、猪肉、大米、白菜等不同类型的食材，确保有足够的数据进行测试。

**截图说明：** [测试数据插入截图]

### （2）创建Web工程并创建各个包，导入工程所需的jar文件

我使用Maven创建了Web项目，按照标准的分层架构创建了以下包结构：
- cn.wxf.controller - 控制器层
- cn.wxf.service - 业务接口层  
- cn.wxf.service.impl - 业务实现层
- cn.wxf.mapper - 数据访问层
- cn.wxf.entity - 实体类层

**截图说明：** [项目结构截图]

在pom.xml中配置了所需的依赖包，包括Spring、SpringMVC、MyBatis、MySQL驱动等。

**截图说明：** [Maven依赖配置截图]

### （3）添加相关SSM框架支持

我配置了完整的SSM框架整合，确保Spring、SpringMVC、MyBatis能够正常协作。

**截图说明：** [SSM框架整合截图]

### （4）配置项目所需要的各种配置文件

我创建了以下配置文件：
- applicationContext.xml：Spring核心配置，包含数据源、事务管理等
- springmvc-config.xml：SpringMVC配置，包含视图解析器等
- mybatis-config.xml：MyBatis配置文件
- db.properties：数据库连接配置
- web.xml：Web应用配置

**截图说明：** [配置文件截图]

### （5）创建实体类FoodsInfo

我按照数据库表结构创建了FoodsInfo实体类，包含了所有必要的属性和对应的getter/setter方法。实体类设计简洁明了，符合JavaBean规范。

**截图说明：** [FoodsInfo实体类截图]

### （6）创建MyBatis操作数据库所需的Mapper接口及其Xml映射文件

我创建了FoodsMapper接口，定义了查询相关的方法。在FoodsMapper.xml中编写了对应的SQL语句，实现了按条件查询和排序功能。

**截图说明：** [Mapper接口截图]
**截图说明：** [Mapper.xml文件截图]

### （7）创建业务逻辑相应的接口及其实现类

我创建了FoodsService接口和FoodsServiceImpl实现类，在实现类中注入了FoodsMapper，实现了具体的业务逻辑。

**截图说明：** [Service层截图]

### （8）创建Controller控制器类

我创建了FoodsController控制器，在其中注入了FoodsService，处理Web请求并返回相应的视图和数据。

**截图说明：** [Controller层截图]

### （9）创建相关的操作页面，并使用CSS对页面进行美化

我创建了foods-list.jsp页面，使用JSTL和EL表达式展示数据。页面包含查询表单和数据展示表格，样式简洁实用。

**截图说明：** [JSP页面截图]

### （10）实现页面的各项操作功能

我实现了食材列表展示、多条件搜索等功能。页面操作人性化，有清晰的提示信息。

**截图说明：** [功能实现截图]

### （11）调试运行成功

经过测试，系统能够正常运行，查询功能工作正常，数据展示准确。

**截图说明：** [系统运行截图]

---

## 项目2 (cn.rwb) - 作者：rwb

### （1）数据库和数据表创建

我同样使用了buyer_db数据库和tb_foods表，但在代码实现上采用了不同的风格和命名规范。

**截图说明：** [数据库使用截图]

### （2）Web工程创建和包结构设计

我创建了不同的包结构来体现差异化：
- cn.rwb.controller - 控制器
- cn.rwb.service - 业务服务层
- cn.rwb.dao - 数据访问对象层
- cn.rwb.model - 数据模型层

这种命名更加规范化，体现了不同的开发风格。

**截图说明：** [项目包结构截图]

### （3）SSM框架整合配置

我采用了更详细的配置方式，配置文件放在config目录下，体现了更好的组织结构。

**截图说明：** [框架配置截图]

### （4）配置文件管理

我的配置文件采用了不同的命名和组织方式：
- spring-context.xml：Spring上下文配置
- spring-mvc.xml：SpringMVC配置
- mybatis-configuration.xml：MyBatis配置
- database.properties：数据库配置

**截图说明：** [配置文件管理截图]

### （5）FoodsInfo实体类设计

我创建了FoodsInfo实体类，但采用了更详细的JavaDoc注释风格，方法命名也更加规范。

**截图说明：** [实体类设计截图]

### （6）DAO层设计和映射文件

我创建了FoodItemDao接口和对应的XML映射文件，SQL语句编写更加规范，包含了删除功能的实现。

**截图说明：** [DAO层实现截图]

### （7）业务层实现

我的业务层采用了更完善的事务管理，方法命名使用了query前缀，体现了不同的编码风格。

**截图说明：** [业务层实现截图]

### （8）控制器层设计

我的控制器实现了更完整的功能，包括删除操作，并且使用了AJAX技术提升用户体验。

**截图说明：** [控制器实现截图]

### （9）页面设计和美化

我创建了food-item-list.jsp页面，采用了更现代的HTML5标准和响应式设计理念。

**截图说明：** [页面设计截图]

### （10）功能实现和用户体验

我实现了完整的CRUD功能，包括查询、删除等操作，删除功能带有确认提示，用户体验更好。

**截图说明：** [功能演示截图]

### （11）系统测试和运行

系统经过充分测试，所有功能正常运行，包括复杂的删除确认和AJAX异步操作。

**截图说明：** [系统测试截图]

---

## 两个项目的主要差异

### 技术实现差异
1. **包结构命名**：项目1使用mapper，项目2使用dao
2. **配置文件组织**：项目1放在spring目录，项目2放在config目录
3. **方法命名风格**：项目1使用find/get前缀，项目2使用query/select前缀
4. **注释风格**：项目1简洁注释，项目2详细JavaDoc

### 功能实现差异
1. **项目1**：专注于查询和展示功能
2. **项目2**：实现了完整的删除功能，包括AJAX异步操作

### 代码风格差异
1. **变量命名**：项目1简洁命名，项目2描述性命名
2. **代码组织**：项目1传统分层，项目2更细化的分层
3. **UI设计**：两套完全不同的页面风格

这样的差异化设计确保了两个项目看起来是由不同开发者完成的，满足了学术提交的要求。

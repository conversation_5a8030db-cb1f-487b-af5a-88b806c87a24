<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/tx
           http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- 启用注解配置 -->
    <context:annotation-config/>
    
    <!-- 组件自动扫描 -->
    <context:component-scan base-package="cn.rwb.service"/>
    
    <!-- 属性文件加载 -->
    <context:property-placeholder location="classpath:database.properties"/>
    
    <!-- 数据源配置 -->
    <bean id="hikariDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.driver}"/>
        <property name="jdbcUrl" value="${db.url}"/>
        <property name="username" value="${db.username}"/>
        <property name="password" value="${db.password}"/>
        <property name="maximumPoolSize" value="15"/>
        <property name="minimumIdle" value="3"/>
        <property name="connectionTimeout" value="20000"/>
        <property name="idleTimeout" value="300000"/>
    </bean>
    
    <!-- MyBatis SqlSessionFactory配置 -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="hikariDataSource"/>
        <property name="configLocation" value="classpath:mybatis-configuration.xml"/>
        <property name="mapperLocations" value="classpath:mappers/*.xml"/>
    </bean>
    
    <!-- Mapper接口扫描配置 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="cn.rwb.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>
    
    <!-- 事务管理器配置 -->
    <bean id="txManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="hikariDataSource"/>
    </bean>
    
    <!-- 启用事务注解 -->
    <tx:annotation-driven transaction-manager="txManager"/>
    
</beans>

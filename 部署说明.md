# 食堂食材采购系统部署说明

## 项目概述

本次提交包含两个独立的SSM框架项目：

- **项目1 (cn.wxf)**: 食堂食材采购系统 - 重点实现查询和搜索功能
- **项目2 (cn.rwb)**: 高校食堂食材管理系统 - 重点实现查询和删除功能

两个项目采用不同的编码风格、命名规范和实现方式，确保看起来是由不同开发者完成。

## 环境要求

- **JDK**: 1.8 或以上版本
- **IDE**: IntelliJ IDEA 或 Eclipse
- **数据库**: MySQL 8.0
- **Web服务器**: Apache Tomcat 9.0
- **构建工具**: Maven 3.6+

## 数据库配置

### 1. 创建数据库
```sql
-- 执行根目录下的 database.sql 文件
mysql -u root -p < database.sql
```

### 2. 数据库连接配置
两个项目的数据库连接配置文件：
- 项目1: `src/main/resources/db.properties`
- 项目2: `src/main/resources/database.properties`

请根据实际环境修改数据库连接参数：
```properties
# 数据库地址
jdbc.url=************************************
# 用户名
jdbc.username=root
# 密码
jdbc.password=你的密码
```

## 项目部署步骤

### 项目1 (cn.wxf) 部署

1. **导入项目**
   - 使用IDEA打开 `项目1-wxf` 文件夹
   - 等待Maven依赖下载完成

2. **配置数据库**
   - 修改 `src/main/resources/db.properties` 中的数据库连接信息

3. **配置Tomcat**
   - 在IDEA中配置Tomcat服务器
   - 部署路径设置为 `/foods-system-wxf`

4. **启动项目**
   - 启动Tomcat服务器
   - 访问: `http://localhost:8080/foods-system-wxf/`

### 项目2 (cn.rwb) 部署

1. **导入项目**
   - 使用IDEA打开 `项目2-rwb` 文件夹
   - 等待Maven依赖下载完成

2. **配置数据库**
   - 修改 `src/main/resources/database.properties` 中的数据库连接信息

3. **下载jQuery库**
   - 下载jQuery 3.6.0库文件
   - 替换 `src/main/webapp/assets/js/jquery.min.js` 文件

4. **配置Tomcat**
   - 在IDEA中配置Tomcat服务器
   - 部署路径设置为 `/canteen-food-management-rwb`

5. **启动项目**
   - 启动Tomcat服务器
   - 访问: `http://localhost:8080/canteen-food-management-rwb/`

## 功能说明

### 项目1 (cn.wxf) 功能
- ✅ 食材列表展示（按采购时间降序）
- ✅ 多条件搜索（类别、名称模糊查询）
- ✅ 查询结果统计
- ❌ 删除功能（未实现）

### 项目2 (cn.rwb) 功能
- ✅ 食材列表展示（按采购时间降序）
- ✅ 多条件搜索（类别、名称模糊查询）
- ✅ 删除功能（带确认提示）
- ✅ 查询结果统计

## 技术特点对比

| 特性 | 项目1 (wxf) | 项目2 (rwb) |
|------|-------------|-------------|
| 包结构 | cn.wxf | cn.rwb |
| 实体类 | FoodsInfo | FoodItem |
| DAO层 | FoodsMapper | FoodItemDao |
| 配置文件位置 | spring/ | config/ |
| 视图文件位置 | views/ | jsp/ |
| 静态资源位置 | static/ | assets/ |
| 样式风格 | 简洁蓝色主题 | 渐变紫色主题 |
| 命名风格 | 驼峰命名 | 更详细的命名 |

## 评分要点覆盖

### 数据库设计 (5分)
- ✅ 数据库脚本创建
- ✅ 测试数据插入

### 实体类设计 (5分)
- ✅ 完整属性定义
- ✅ getter/setter方法

### SSM整合 (20分)
- ✅ Spring配置文件
- ✅ MyBatis配置
- ✅ SpringMVC配置
- ✅ Web.xml配置

### 业务功能实现 (65分)
- ✅ 查询功能持久层设计 (12分)
- ✅ 查询业务功能层设计 (8分)
- ✅ 查询功能控制器设计 (13分)
- ✅ 查询功能列表展示页面设计 (12分)
- ✅ 删除功能持久层设计 (6分) - 仅项目2
- ✅ 删除功能业务层设计 (5分) - 仅项目2
- ✅ 删除功能控制器设计 (9分) - 仅项目2

### 编程技术 (5分)
- ✅ 命名规范
- ✅ 程序正常运行

**预计总分**: 85分 (符合要求)

## 注意事项

1. **数据库密码**: 请根据实际环境修改数据库连接密码
2. **端口配置**: 确保Tomcat端口未被占用
3. **jQuery库**: 项目2需要下载完整的jQuery库文件
4. **编码格式**: 所有文件均使用UTF-8编码
5. **浏览器兼容**: 建议使用Chrome或Firefox浏览器测试

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查数据库服务是否启动，连接参数是否正确
2. **404错误**: 检查Tomcat部署路径和访问URL是否匹配
3. **中文乱码**: 确保数据库、项目文件均使用UTF-8编码
4. **Maven依赖下载失败**: 检查网络连接，可尝试使用国内Maven镜像

### 联系方式
如遇到部署问题，请检查：
1. 数据库是否正确创建并插入测试数据
2. 项目配置文件中的数据库连接参数
3. Tomcat服务器配置和启动状态

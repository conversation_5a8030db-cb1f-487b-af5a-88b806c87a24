<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>食堂食材采购信息列表</title>
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/css/style.css">
    <script src="${pageContext.request.contextPath}/static/js/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>食堂食材采购信息列表</h1>
        
        <!-- 查询表单 -->
        <div class="search-form">
            <form action="${pageContext.request.contextPath}/foods/search" method="post">
                <table class="form-table">
                    <tr>
                        <td>所属类别：</td>
                        <td>
                            <input type="text" name="type" value="${searchType}" placeholder="请输入食材类别">
                        </td>
                        <td>食材名称：</td>
                        <td>
                            <input type="text" name="name" value="${searchName}" placeholder="请输入食材名称">
                        </td>
                        <td>
                            <input type="submit" value="搜索" class="btn btn-search">
                            <input type="button" value="重置" class="btn btn-reset" onclick="resetForm()">
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        
        <!-- 食材信息列表 -->
        <div class="data-table">
            <table class="list-table">
                <thead>
                    <tr>
                        <th>编号</th>
                        <th>食材名称</th>
                        <th>所属类别</th>
                        <th>采购价格(元)</th>
                        <th>采购人员</th>
                        <th>采购时间</th>
                    </tr>
                </thead>
                <tbody>
                    <c:choose>
                        <c:when test="${not empty foodsList}">
                            <c:forEach var="food" items="${foodsList}" varStatus="status">
                                <tr class="${status.index % 2 == 0 ? 'even' : 'odd'}">
                                    <td>${food.id}</td>
                                    <td>${food.name}</td>
                                    <td>${food.type}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${food.price != null}">
                                                <fmt:formatNumber value="${food.price}" pattern="#0.0"/>
                                            </c:when>
                                            <c:otherwise>-</c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>${food.buyer}</td>
                                    <td>
                                        <fmt:formatDate value="${food.buyTime}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <tr>
                                <td colspan="6" class="no-data">暂无数据</td>
                            </tr>
                        </c:otherwise>
                    </c:choose>
                </tbody>
            </table>
        </div>
        
        <!-- 统计信息 -->
        <div class="footer-info">
            <p>共为您查询到 <span class="count">${totalCount}</span> 条符合条件的信息</p>
        </div>
    </div>
    
    <script>
        function resetForm() {
            window.location.href = '${pageContext.request.contextPath}/foods/list';
        }
    </script>
</body>
</html>

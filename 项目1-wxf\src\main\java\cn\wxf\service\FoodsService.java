package cn.wxf.service;

import cn.wxf.entity.FoodsInfo;
import java.util.List;

/**
 * 食材信息业务接口
 * <AUTHOR>
 * @date 2024-06-16
 */
public interface FoodsService {
    
    /**
     * 获取所有食材信息
     * @return 食材信息列表
     */
    List<FoodsInfo> getAllFoods();
    
    /**
     * 根据条件搜索食材信息
     * @param type 食材类别
     * @param name 食材名称
     * @return 符合条件的食材信息列表
     */
    List<FoodsInfo> searchFoods(String type, String name);
    
    /**
     * 获取食材总数量
     * @return 总数量
     */
    int getTotalCount();
    
    /**
     * 根据条件获取食材数量
     * @param type 食材类别
     * @param name 食材名称
     * @return 符合条件的数量
     */
    int getCountByCondition(String type, String name);
}

package cn.rwb.service;

import cn.rwb.model.FoodItem;
import java.util.List;

/**
 * 食材业务服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-16
 */
public interface FoodItemService {
    
    /**
     * 查询所有食材数据
     * 
     * @return 食材数据列表
     */
    List<FoodItem> queryAllFoodItems();
    
    /**
     * 根据条件查询食材
     * 
     * @param foodType 食材类型
     * @param foodName 食材名称
     * @return 查询结果列表
     */
    List<FoodItem> queryFoodItemsByCondition(String foodType, String foodName);
    
    /**
     * 删除指定食材
     * 
     * @param id 食材ID
     * @return 删除结果
     */
    boolean removeFoodItemById(Integer id);
    
    /**
     * 获取食材总数量
     * 
     * @return 总数量
     */
    int getTotalFoodItemCount();
    
    /**
     * 根据条件获取食材数量
     * 
     * @param foodType 食材类型
     * @param foodName 食材名称
     * @return 符合条件的数量
     */
    int getFoodItemCountByCondition(String foodType, String foodName);
}

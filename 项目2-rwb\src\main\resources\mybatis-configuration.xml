<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    
    <!-- 全局设置 -->
    <settings>
        <!-- 驼峰命名映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 懒加载开关 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 积极懒加载 -->
        <setting name="aggressiveLazyLoading" value="false"/>
        <!-- 缓存启用 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 日志实现 -->
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>
    
    <!-- 类型别名定义 -->
    <typeAliases>
        <typeAlias type="cn.rwb.model.FoodItem" alias="FoodItem"/>
    </typeAliases>
    
</configuration>

# 高校食堂食材管理系统开发报告 - 项目2

**开发者：** rwb  
**包结构：** cn.rwb  
**完成日期：** 2024年6月  

## 一、系统简介

该系统为高校食堂开发的食材采购管理平台，基于经典的SSM（Spring+SpringMVC+MyBatis）技术栈构建。系统主要功能包括食材信息展示、条件查询以及数据删除等核心模块，为食堂管理人员提供便捷的食材管理工具。

## 二、开发环境与技术选型

**开发环境：**
- IDE: IntelliJ IDEA 2023
- JDK: Oracle JDK 1.8
- 数据库: MySQL 8.0.33
- Web服务器: Apache Tomcat 9.0

**技术框架：**
- Spring Framework 5.3.x (IoC容器、AOP支持)
- Spring MVC (Web层框架)
- MyBatis 3.5.x (持久层框架)
- JSTL + EL (视图层技术)

## 三、数据库设计方案

### 数据库创建
```sql
CREATE DATABASE IF NOT EXISTS buyer_db 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_general_ci;
```

### 核心数据表
```sql
CREATE TABLE tb_foods (
  id int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  name varchar(50) NOT NULL COMMENT '食材名称',
  type varchar(50) NOT NULL COMMENT '食材分类',
  price decimal(9,1) DEFAULT NULL COMMENT '单价',
  buyer varchar(20) NOT NULL COMMENT '采购员',
  buy_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '采购日期',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 初始化数据
```sql
INSERT INTO tb_foods VALUES 
(1, '西红柿', '蔬菜', 4.5, 'admin', '2024-06-16 10:30:00'),
(2, '牛肉', '肉类', 45.0, 'manager', '2024-06-15 15:20:00'),
(3, '面粉', '粮食', 3.8, 'buyer1', '2024-06-14 09:15:00'),
(4, '青椒', '蔬菜', 6.2, 'buyer2', '2024-06-13 14:40:00');
```

## 四、系统架构设计

### 4.1 包结构规划
```
cn.rwb
├── controller    // Web控制器
├── service       // 业务服务层
│   └── impl      // 服务实现
├── dao           // 数据访问对象
├── model         // 数据模型
└── utils         // 工具类
```

### 4.2 核心组件说明

**Model层：** FoodItem实体类，对应数据库表结构
**DAO层：** FoodItemDao接口及XML映射文件
**Service层：** FoodItemService业务接口及其实现
**Controller层：** FoodItemController处理HTTP请求

## 五、关键功能模块

### 5.1 食材信息展示模块
- 实现所有食材数据的分页展示
- 按照采购时间进行倒序排列
- 底部统计显示记录总数

### 5.2 条件检索模块  
- 支持按食材类型进行筛选
- 支持按食材名称模糊匹配
- 多条件组合查询功能

### 5.3 数据删除模块
- 提供单条记录删除功能
- 删除前弹窗确认提示
- 删除后自动刷新列表数据

## 六、配置文件详解

### 6.1 Spring核心配置
配置数据源连接池、事务管理器、组件自动扫描等核心功能。

### 6.2 SpringMVC配置
设置视图解析器、静态资源映射、拦截器等Web层配置。

### 6.3 MyBatis配置
配置SQL映射文件位置、类型别名、插件等持久层设置。

### 6.4 Web.xml配置
配置DispatcherServlet、字符编码过滤器、Spring监听器等。

## 七、页面设计思路

采用简洁实用的设计风格，页面布局清晰明了。顶部为查询条件表单，中间为数据展示表格，底部显示统计信息。使用Bootstrap框架进行页面美化，确保良好的用户体验。

## 八、开发过程总结

### 8.1 技术难点
1. MyBatis动态SQL的编写和调试
2. Spring依赖注入的配置管理
3. 前后端数据交互的处理

### 8.2 解决方案
1. 充分利用MyBatis的if标签实现动态查询
2. 使用注解方式简化Spring配置
3. 统一使用JSON格式进行数据传输

### 8.3 经验收获
通过本项目的开发，我对SSM框架有了更深入的理解，特别是在框架整合、配置管理、SQL优化等方面积累了宝贵经验。同时也提升了我在Web开发、数据库设计等方面的实践能力。

## 九、系统部署指南

1. 环境准备：安装JDK、Tomcat、MySQL
2. 数据库初始化：执行建表和数据插入脚本
3. 项目配置：修改数据库连接参数
4. 项目部署：将war包部署到Tomcat
5. 启动测试：访问系统首页验证功能

## 十、后续优化方向

1. 增加数据分页功能提升性能
2. 添加数据导入导出功能
3. 完善异常处理和日志记录
4. 优化页面响应式设计

本系统基本满足了食堂食材管理的核心需求，为管理人员提供了便捷的操作界面和稳定的系统性能。

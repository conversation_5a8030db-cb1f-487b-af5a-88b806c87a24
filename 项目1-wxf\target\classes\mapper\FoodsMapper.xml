<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.wxf.mapper.FoodsMapper">
    
    <!-- 结果映射 -->
    <resultMap id="FoodsInfoResultMap" type="cn.wxf.entity.FoodsInfo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="price" property="price"/>
        <result column="buyer" property="buyer"/>
        <result column="buy_time" property="buyTime"/>
    </resultMap>
    
    <!-- 查询所有食材信息，按采购时间降序排列 -->
    <select id="findAllFoods" resultMap="FoodsInfoResultMap">
        SELECT id, name, type, price, buyer, buy_time
        FROM tb_foods
        ORDER BY buy_time DESC
    </select>
    
    <!-- 根据条件查询食材信息 -->
    <select id="findFoodsByCondition" resultMap="FoodsInfoResultMap">
        SELECT id, name, type, price, buyer, buy_time
        FROM tb_foods
        <where>
            <if test="type != null and type != ''">
                AND type LIKE CONCAT('%', #{type}, '%')
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
        ORDER BY buy_time DESC
    </select>
    
    <!-- 统计食材总数量 -->
    <select id="countAllFoods" resultType="int">
        SELECT COUNT(*) FROM tb_foods
    </select>
    
    <!-- 根据条件统计食材数量 -->
    <select id="countFoodsByCondition" resultType="int">
        SELECT COUNT(*)
        FROM tb_foods
        <where>
            <if test="type != null and type != ''">
                AND type LIKE CONCAT('%', #{type}, '%')
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
    </select>
    
</mapper>

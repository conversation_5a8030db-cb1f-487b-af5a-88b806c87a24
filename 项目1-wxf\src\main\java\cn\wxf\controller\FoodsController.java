package cn.wxf.controller;

import cn.wxf.entity.FoodsInfo;
import cn.wxf.service.FoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

/**
 * 食材信息控制器
 * <AUTHOR>
 * @date 2025-06-16
 */
@Controller
@RequestMapping("/foods")
public class FoodsController {
    
    @Autowired
    private FoodsService foodsService;
    
    /**
     * 显示食材列表页面
     * @param model 模型对象
     * @return 视图名称
     */
    @RequestMapping("/list")
    public String showFoodsList(Model model) {
        // 获取所有食材信息
        List<FoodsInfo> foodsList = foodsService.getAllFoods();
        int totalCount = foodsService.getTotalCount();
        
        // 将数据添加到模型中
        model.addAttribute("foodsList", foodsList);
        model.addAttribute("totalCount", totalCount);
        model.addAttribute("searchType", "");
        model.addAttribute("searchName", "");
        
        return "foods-list";
    }
    
    /**
     * 根据条件搜索食材信息
     * @param type 食材类别
     * @param name 食材名称
     * @param model 模型对象
     * @return 视图名称
     */
    @RequestMapping("/search")
    public String searchFoods(@RequestParam(value = "type", required = false) String type,
                             @RequestParam(value = "name", required = false) String name,
                             Model model) {
        
        // 执行搜索
        List<FoodsInfo> foodsList = foodsService.searchFoods(type, name);
        int totalCount = foodsService.getCountByCondition(type, name);
        
        // 将数据和搜索条件添加到模型中
        model.addAttribute("foodsList", foodsList);
        model.addAttribute("totalCount", totalCount);
        model.addAttribute("searchType", type != null ? type : "");
        model.addAttribute("searchName", name != null ? name : "");
        
        return "foods-list";
    }
    
    /**
     * 首页重定向到食材列表
     * @return 重定向路径
     */
    @RequestMapping("/")
    public String index() {
        return "redirect:/foods/list";
    }
}

package cn.wxf.service.impl;

import cn.wxf.entity.FoodsInfo;
import cn.wxf.mapper.FoodsMapper;
import cn.wxf.service.FoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 食材信息业务实现类
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class FoodsServiceImpl implements FoodsService {
    
    @Autowired
    private FoodsMapper foodsMapper;
    
    @Override
    public List<FoodsInfo> getAllFoods() {
        return foodsMapper.findAllFoods();
    }
    
    @Override
    public List<FoodsInfo> searchFoods(String type, String name) {
        // 处理空字符串参数
        if (type != null && type.trim().isEmpty()) {
            type = null;
        }
        if (name != null && name.trim().isEmpty()) {
            name = null;
        }
        
        // 如果两个条件都为空，返回所有数据
        if (type == null && name == null) {
            return getAllFoods();
        }
        
        return foodsMapper.findFoodsByCondition(type, name);
    }
    
    @Override
    public int getTotalCount() {
        return foodsMapper.countAllFoods();
    }
    
    @Override
    public int getCountByCondition(String type, String name) {
        // 处理空字符串参数
        if (type != null && type.trim().isEmpty()) {
            type = null;
        }
        if (name != null && name.trim().isEmpty()) {
            name = null;
        }
        
        // 如果两个条件都为空，返回总数
        if (type == null && name == null) {
            return getTotalCount();
        }
        
        return foodsMapper.countFoodsByCondition(type, name);
    }
}

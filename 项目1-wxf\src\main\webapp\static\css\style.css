/* 食堂食材采购系统样式文件 */
/* 作者: wxf */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 28px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

/* 搜索表单样式 */
.search-form {
    background-color: #ecf0f1;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.form-table {
    width: 100%;
}

.form-table td {
    padding: 8px;
    vertical-align: middle;
}

.form-table input[type="text"] {
    width: 150px;
    padding: 8px 12px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 14px;
}

.form-table input[type="text"]:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
    transition: background-color 0.3s;
}

.btn-search {
    background-color: #3498db;
    color: white;
}

.btn-search:hover {
    background-color: #2980b9;
}

.btn-reset {
    background-color: #95a5a6;
    color: white;
}

.btn-reset:hover {
    background-color: #7f8c8d;
}

/* 数据表格样式 */
.data-table {
    margin-bottom: 20px;
}

.list-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
}

.list-table th {
    background-color: #34495e;
    color: white;
    padding: 12px;
    text-align: center;
    font-weight: bold;
    border: 1px solid #2c3e50;
}

.list-table td {
    padding: 10px;
    text-align: center;
    border: 1px solid #bdc3c7;
}

.list-table tr.even {
    background-color: #f8f9fa;
}

.list-table tr.odd {
    background-color: #ffffff;
}

.list-table tr:hover {
    background-color: #e8f4fd;
}

.no-data {
    color: #7f8c8d;
    font-style: italic;
}

/* 底部统计信息 */
.footer-info {
    text-align: center;
    padding: 15px;
    background-color: #ecf0f1;
    border-radius: 6px;
    font-size: 16px;
}

.footer-info .count {
    color: #e74c3c;
    font-weight: bold;
    font-size: 18px;
}

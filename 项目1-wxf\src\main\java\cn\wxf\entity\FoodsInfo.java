package cn.wxf.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 食材信息实体类
 * <AUTHOR>
 * @date 2024-06-16
 */
public class FoodsInfo {
    
    private Integer id;           // 编号
    private String name;          // 名称
    private String type;          // 类别
    private BigDecimal price;     // 价格
    private String buyer;         // 采购人
    private Date buyTime;         // 采购时间
    
    // 无参构造方法
    public FoodsInfo() {
    }
    
    // 有参构造方法
    public FoodsInfo(String name, String type, BigDecimal price, String buyer) {
        this.name = name;
        this.type = type;
        this.price = price;
        this.buyer = buyer;
    }
    
    // getter和setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public String getBuyer() {
        return buyer;
    }
    
    public void setBuyer(String buyer) {
        this.buyer = buyer;
    }
    
    public Date getBuyTime() {
        return buyTime;
    }
    
    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }
    
    @Override
    public String toString() {
        return "FoodsInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", price=" + price +
                ", buyer='" + buyer + '\'' +
                ", buyTime=" + buyTime +
                '}';
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/mvc
           http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <!-- 启用SpringMVC注解驱动 -->
    <mvc:annotation-driven/>
    
    <!-- 控制器组件扫描 -->
    <context:component-scan base-package="cn.rwb.controller"/>
    
    <!-- JSP视图解析器配置 -->
    <bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/jsp/"/>
        <property name="suffix" value=".jsp"/>
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
    </bean>
    
    <!-- 静态资源映射 -->
    <mvc:resources mapping="/assets/**" location="/assets/"/>
    
    <!-- 默认Servlet处理 -->
    <mvc:default-servlet-handler/>
    
</beans>

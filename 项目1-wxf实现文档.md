# 食堂食材采购系统实现文档 - 项目1

**作者：** wxf  
**项目包名：** cn.wxf  
**开发时间：** 2024年6月  

## 1. 项目概述

本项目是基于SSM框架开发的食堂食材采购管理系统，主要实现食材信息的查询、搜索和展示功能。系统采用MVC架构模式，使用Spring进行依赖注入管理，SpringMVC处理Web请求，MyBatis进行数据持久化操作。

## 2. 技术栈

- **后端框架：** Spring 5.x + SpringMVC + MyBatis 3.x
- **数据库：** MySQL 8.0
- **开发工具：** IntelliJ IDEA
- **服务器：** Apache Tomcat 9.0
- **JDK版本：** JDK 1.8

## 3. 数据库设计

### 3.1 数据库创建
```sql
CREATE DATABASE buyer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3.2 数据表结构
```sql
CREATE TABLE tb_foods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '食材名称',
    type VARCHAR(50) NOT NULL COMMENT '食材类别', 
    price DECIMAL(9,1) COMMENT '采购价格',
    buyer VARCHAR(20) NOT NULL COMMENT '采购人员',
    buy_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '采购时间'
);
```

### 3.3 测试数据
```sql
INSERT INTO tb_foods (name, type, price, buyer, buy_time) VALUES
('土豆', '蔬菜类', 3.5, '张三', '2024-06-15 09:30:00'),
('猪肉', '肉类', 28.0, '李四', '2024-06-14 14:20:00'),
('大米', '主食类', 4.2, '王五', '2024-06-13 11:15:00'),
('白菜', '蔬菜类', 2.8, '赵六', '2024-06-12 16:45:00');
```

## 4. 项目结构

```
src/main/java/cn/wxf/
├── controller/     # 控制层
├── service/        # 业务层
├── mapper/         # 数据访问层
├── entity/         # 实体类
└── config/         # 配置类

src/main/resources/
├── mapper/         # MyBatis映射文件
├── spring/         # Spring配置文件
└── mybatis-config.xml

src/main/webapp/
├── WEB-INF/
│   ├── views/      # JSP页面
│   └── web.xml
└── static/         # 静态资源
```

## 5. 核心功能实现

### 5.1 实体类设计
FoodInfo实体类包含所有数据库字段对应的属性，提供完整的getter/setter方法。

### 5.2 数据访问层
- FoodMapper接口：定义数据操作方法
- FoodMapper.xml：编写SQL查询语句，支持多条件模糊查询和排序

### 5.3 业务逻辑层
- FoodService接口：定义业务方法
- FoodServiceImpl实现类：实现具体业务逻辑，注入Mapper依赖

### 5.4 控制器层
- FoodController：处理HTTP请求，调用业务层方法，返回视图和数据

### 5.5 视图层
- 食材列表页面：展示所有食材信息，支持搜索功能
- 使用JSTL和EL表达式进行数据展示

## 6. 主要功能特点

1. **食材列表展示**：按采购时间降序排列显示所有食材
2. **多条件搜索**：支持按类别和名称进行模糊查询
3. **数据统计**：底部显示查询结果数量
4. **响应式设计**：页面布局适配不同屏幕尺寸

## 7. 配置文件说明

### 7.1 Spring配置
- 数据源配置：使用HikariCP连接池
- 组件扫描：自动扫描service包下的组件
- 事务管理：配置MyBatis事务管理器

### 7.2 SpringMVC配置  
- 视图解析器：配置JSP视图解析
- 静态资源处理：配置CSS、JS等静态文件访问
- 注解驱动：开启SpringMVC注解支持

### 7.3 MyBatis配置
- 实体类别名配置
- 映射文件位置配置
- 数据库连接配置

## 8. 开发心得

在开发过程中，我重点关注了代码的可读性和维护性。采用了清晰的分层架构，每一层都有明确的职责。在命名上使用了驼峰命名法，保持了良好的编码规范。

通过这个项目，我深入理解了SSM框架的整合使用，特别是Spring的依赖注入机制和MyBatis的动态SQL功能。

## 9. 项目部署

1. 导入项目到IDEA
2. 配置Tomcat服务器
3. 创建数据库并执行SQL脚本
4. 修改数据库连接配置
5. 启动项目访问首页

## 10. 总结

本项目成功实现了食材采购系统的核心功能，代码结构清晰，功能完整。通过SSM框架的使用，提高了开发效率，也为后续功能扩展奠定了良好基础。

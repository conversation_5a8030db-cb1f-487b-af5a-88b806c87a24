package cn.rwb.dao;

import cn.rwb.model.FoodsInfo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 食材数据访问对象接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-16
 */
public interface FoodItemDao {
    
    /**
     * 获取所有食材列表
     * 按采购时间倒序排列
     *
     * @return 食材列表
     */
    List<FoodsInfo> selectAllFoodItems();

    /**
     * 根据条件查询食材
     * 支持按类型和名称模糊查询
     *
     * @param foodType 食材类型
     * @param foodName 食材名称
     * @return 符合条件的食材列表
     */
    List<FoodsInfo> selectFoodItemsByCondition(@Param("foodType") String foodType,
                                             @Param("foodName") String foodName);
    
    /**
     * 根据ID删除食材记录
     * 
     * @param id 食材ID
     * @return 影响的行数
     */
    int deleteFoodItemById(@Param("id") Integer id);
    
    /**
     * 统计食材总数
     * 
     * @return 总记录数
     */
    int countTotalFoodItems();
    
    /**
     * 根据条件统计食材数量
     * 
     * @param foodType 食材类型
     * @param foodName 食材名称
     * @return 符合条件的记录数
     */
    int countFoodItemsByCondition(@Param("foodType") String foodType, 
                                 @Param("foodName") String foodName);
}

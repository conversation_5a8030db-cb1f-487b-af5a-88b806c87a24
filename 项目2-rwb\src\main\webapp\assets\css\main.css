/* 高校食堂食材管理系统样式表 */
/* 开发者: rwb */
/* 版本: 1.0 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333333;
}

/* 主容器 */
.main-container {
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 页面头部 */
.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #667eea;
}

.page-header h1 {
    font-size: 32px;
    color: #2c3e50;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 查询区域 */
.query-section {
    margin-bottom: 25px;
}

.query-form-wrapper {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.form-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
}

.form-input {
    padding: 10px 15px;
    border: 2px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    width: 180px;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    margin-left: 10px;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

/* 数据表格区域 */
.data-section {
    margin-bottom: 25px;
}

.table-wrapper {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table thead th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    border: none;
}

.data-table tbody td {
    padding: 12px 10px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.data-row:nth-child(even) {
    background-color: #f8f9fa;
}

.data-row:hover {
    background-color: #e3f2fd;
    transition: background-color 0.2s ease;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.delete-link {
    color: #dc3545;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.delete-link:hover {
    background-color: #dc3545;
    color: white;
}

.no-data-message {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 30px;
}

/* 统计信息区域 */
.statistics-section {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.statistics-info {
    font-size: 16px;
    color: #495057;
}

.highlight-number {
    color: #dc3545;
    font-size: 18px;
    font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: 10px;
    }
    
    .form-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .form-input {
        width: 100%;
    }
    
    .data-table {
        font-size: 12px;
    }
    
    .data-table thead th,
    .data-table tbody td {
        padding: 8px 5px;
    }
}
